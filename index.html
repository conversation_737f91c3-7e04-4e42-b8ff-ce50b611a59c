<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dapper – The Demand Agency</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 1rem 0;
            border-bottom: 1px solid #eee;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #000;
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: #007bff;
        }

        .cta-button {
            background: #007bff;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s;
        }

        .cta-button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        /* Hero Section */
        .hero {
            padding: 120px 0 80px;
            text-align: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #000;
        }

        .hero h2 {
            font-size: 2rem;
            font-weight: 400;
            margin-bottom: 2rem;
            color: #666;
        }

        .hero-description {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: #666;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .hero-cta {
            background: #007bff;
            color: white;
            padding: 1rem 2rem;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            display: inline-block;
            transition: all 0.3s;
        }

        .hero-cta:hover {
            background: #0056b3;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 123, 255, 0.3);
        }

        /* Results Section */
        .results {
            padding: 80px 0;
            background: #fff;
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-header h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #000;
        }

        .section-header p {
            font-size: 1.2rem;
            color: #666;
            max-width: 600px;
            margin: 0 auto;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .result-card {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            transition: transform 0.3s;
        }

        .result-card:hover {
            transform: translateY(-5px);
        }

        .result-number {
            font-size: 3rem;
            font-weight: 700;
            color: #007bff;
            margin-bottom: 0.5rem;
        }

        .result-text {
            font-size: 1.1rem;
            color: #666;
        }

        /* Services Section */
        .services {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .service-card {
            background: white;
            padding: 2.5rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .service-card h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #000;
        }

        .service-card p {
            color: #666;
            line-height: 1.6;
        }

        /* Testimonials */
        .testimonials {
            padding: 80px 0;
            background: #fff;
        }

        .testimonial-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .testimonial {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 15px;
            border-left: 4px solid #007bff;
        }

        .testimonial-text {
            font-size: 1.1rem;
            font-style: italic;
            margin-bottom: 1rem;
            color: #333;
        }

        .testimonial-author {
            font-weight: 600;
            color: #000;
        }

        .testimonial-company {
            color: #666;
            font-size: 0.9rem;
        }

        /* Footer */
        footer {
            background: #000;
            color: white;
            padding: 60px 0 30px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h4 {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: #fff;
        }

        .footer-section a {
            color: #ccc;
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
            transition: color 0.3s;
        }

        .footer-section a:hover {
            color: #007bff;
        }

        .footer-bottom {
            border-top: 1px solid #333;
            padding-top: 2rem;
            text-align: center;
            color: #666;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero h2 {
                font-size: 1.5rem;
            }

            .results-grid,
            .services-grid,
            .testimonial-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <nav class="container">
            <a href="#" class="logo">Dapper</a>
            <ul class="nav-links">
                <li><a href="#services">Services</a></li>
                <li><a href="#expertise">Expertise</a></li>
                <li><a href="#cases">Cases</a></li>
                <li><a href="#resources">Resources</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#careers">Careers</a></li>
            </ul>
            <a href="#contact" class="cta-button">Talk to us</a>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1>B2B Marketing Agency</h1>
            <h2>We build high-performing marketing engines for B2B Brands</h2>
            <p class="hero-description">
                We build, optimize, and scale marketing engines that generate pipeline and improve marketing ROI.
            </p>
            <a href="#contact" class="hero-cta">Discover more</a>
        </div>
    </section>

    <!-- Results Section -->
    <section class="results" id="results">
        <div class="container">
            <div class="section-header">
                <h2>100+ B2B Companies trusted us to improve their marketing</h2>
                <p>More than 100 B2B companies worldwide trusted us to improve their marketing engine and marketing ROI.</p>
            </div>
            <div class="results-grid">
                <div class="result-card">
                    <div class="result-number">200%</div>
                    <div class="result-text">More inbound sales calls</div>
                </div>
                <div class="result-card">
                    <div class="result-number">53%</div>
                    <div class="result-text">More qualified pipeline</div>
                </div>
                <div class="result-card">
                    <div class="result-number">60+</div>
                    <div class="result-text">Inbound Leads</div>
                </div>
                <div class="result-card">
                    <div class="result-number">66%</div>
                    <div class="result-text">Win rate</div>
                </div>
                <div class="result-card">
                    <div class="result-number">400%</div>
                    <div class="result-text">High-intent Downloads</div>
                </div>
                <div class="result-card">
                    <div class="result-number">+54%</div>
                    <div class="result-text">Increase Inbound Pipeline</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services" id="services">
        <div class="container">
            <div class="section-header">
                <h2>Level up your marketing, improve marketing ROI</h2>
                <p>Better marketing leads to better marketing ROI. At Dapper, we help you level up your complete marketing engine. From strategy to content, advertising, and measurement.</p>
            </div>
            <div class="services-grid">
                <div class="service-card">
                    <h3>Content & Creative</h3>
                    <p>We'll make your prospects stop scrolling with captivating content and creative that shows you're the absolute authority in solving their problems.</p>
                </div>
                <div class="service-card">
                    <h3>Paid Media & Performance</h3>
                    <p>Build, optimize and scale your performance marketing to capture active demand and turn it into pipeline.</p>
                </div>
                <div class="service-card">
                    <h3>Data & Measurement</h3>
                    <p>We make the invisible visible with comprehensive tracking and measurement that proves marketing ROI.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Demand Generation Section -->
    <section class="services" style="background: #fff;">
        <div class="container">
            <div class="section-header">
                <h2>Demand Gen</h2>
                <h3 style="font-size: 1.5rem; color: #666; margin-top: 1rem;">Become famous in your niche and build demand</h3>
            </div>
            <div style="max-width: 800px; margin: 0 auto; text-align: left;">
                <p style="font-size: 1.1rem; margin-bottom: 1.5rem; font-weight: 600;">B2B buyers buy differently. You cannot 'force' a need. But as soon as a need arises, you want to be the first one your prospect thinks of.</p>
                <p style="margin-bottom: 1.5rem;">That is why you want your brand to be top-of-mind in your niche category. So when a need arises, the ideal client thinks of you. We call this Niche Famous™ at Dapper.</p>
                <p style="margin-bottom: 1.5rem;">How do you become Niche Famous™? By being visible in your niche of ideal customers constantly with captivating content that shows you're the absolute authority in solving their problems.</p>
                <p style="margin-bottom: 2rem;">You need content and creative so good that your prospect can't ignore it, and distribution strategies that ensure you're visible everywhere and always for your ideal clients.</p>

                <h4 style="font-size: 1.3rem; margin-bottom: 1rem; color: #000;">The result?</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <div style="text-align: center; padding: 1rem;">
                        <div style="font-size: 1.1rem; font-weight: 600; color: #007bff;">Growing pipeline</div>
                    </div>
                    <div style="text-align: center; padding: 1rem;">
                        <div style="font-size: 1.1rem; font-weight: 600; color: #007bff;">Shorter Sales cycles</div>
                    </div>
                    <div style="text-align: center; padding: 1rem;">
                        <div style="font-size: 1.1rem; font-weight: 600; color: #007bff;">Better ICP-fit inbound leads</div>
                    </div>
                    <div style="text-align: center; padding: 1rem;">
                        <div style="font-size: 1.1rem; font-weight: 600; color: #007bff;">Compounding brand building</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials">
        <div class="container">
            <div class="section-header">
                <h2>Driven by a performance mindset</h2>
                <p>You don't just hire experts - you hire people with a drive to deliver results. The Dapper team thrives on impact. When you work with us, you'll work with a team as ambitious about growth as you are.</p>
            </div>
            <div class="testimonial-grid">
                <div class="testimonial">
                    <div class="testimonial-text">"We saw a 200% increase in Sales Qualified Leads."</div>
                    <div class="testimonial-author">Reza Schott</div>
                    <div class="testimonial-company">Head of Marketing - OPP</div>
                </div>
                <div class="testimonial">
                    <div class="testimonial-text">"This is the first marketing agency I've worked with where I see results."</div>
                    <div class="testimonial-author">Machiel Kunst</div>
                    <div class="testimonial-company">Bluebird</div>
                </div>
                <div class="testimonial">
                    <div class="testimonial-text">"I know exactly what I can expect from the Dapper team. They move fast, deliver good quality, and make clear decisions."</div>
                    <div class="testimonial-author">Angela Hordijk</div>
                    <div class="testimonial-company">Marketing Manager Video Collaboration - Logitech</div>
                </div>
                <div class="testimonial">
                    <div class="testimonial-text">"Since we started with Dapper we finally have prospects reaching out to us, instead of relying on outbound."</div>
                    <div class="testimonial-author">George Borst</div>
                    <div class="testimonial-company">Business Development Lead - FOCUS-ON</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="contact">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Contact</h4>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                    <a href="tel:+***********">+31 10 307 6707</a>
                    <p style="margin-top: 1rem; color: #ccc;">
                        Dapper Rotterdam<br>
                        Weena 70, 13th floor<br>
                        3012 CM Rotterdam
                    </p>
                </div>
                <div class="footer-section">
                    <h4>Services</h4>
                    <a href="#services">Content & Creative</a>
                    <a href="#services">Paid Media & Performance</a>
                    <a href="#services">Data & Measurement</a>
                    <a href="#services">Demand Team</a>
                    <a href="#services">Demand Gen Training</a>
                </div>
                <div class="footer-section">
                    <h4>Expertise</h4>
                    <a href="#expertise">B2B SaaS</a>
                    <a href="#expertise">B2B Service</a>
                    <a href="#expertise">B2B Hardware</a>
                </div>
                <div class="footer-section">
                    <h4>Company</h4>
                    <a href="#about">About</a>
                    <a href="#careers">Careers</a>
                    <a href="#cases">Cases</a>
                    <a href="#resources">Blog</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Dapper. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add scroll effect to header
        window.addEventListener('scroll', function() {
            const header = document.querySelector('header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = 'none';
            }
        });
    </script>
</body>
</html>
