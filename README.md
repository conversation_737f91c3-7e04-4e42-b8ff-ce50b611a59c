# Dapper Agency Homepage Clone

This is a recreated version of the Dapper Agency homepage (https://www.dapper.agency/) that you can run locally.

## Features

- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Modern Styling**: Clean, professional design with smooth animations
- **Interactive Elements**: Hover effects, smooth scrolling navigation
- **Complete Sections**:
  - Hero section with main value proposition
  - Results showcase with key metrics
  - Services overview
  - Demand Generation explanation
  - Client testimonials
  - Contact information and footer

## How to Run

### Method 1: Using Python (Recommended)

1. Make sure you have Python installed on your system
2. Open a terminal/command prompt in this directory
3. Run the server:
   ```bash
   python server.py
   ```
   or
   ```bash
   python3 server.py
   ```
4. The website will automatically open in your browser at `http://localhost:8000`
5. Press `Ctrl+C` to stop the server

### Method 2: Direct File Opening

1. Simply double-click on `index.html` to open it in your default browser
2. Note: Some features may not work properly when opening directly from file system

### Method 3: Using Node.js (if you have it installed)

1. Install a simple HTTP server:
   ```bash
   npm install -g http-server
   ```
2. Run the server:
   ```bash
   http-server
   ```
3. Open `http://localhost:8080` in your browser

## File Structure

```
├── index.html          # Main HTML file with embedded CSS and JavaScript
├── server.py          # Python server script for local development
└── README.md          # This file
```

## Key Sections

1. **Header**: Fixed navigation with smooth scrolling links
2. **Hero**: Main value proposition and call-to-action
3. **Results**: Key performance metrics and success stories
4. **Services**: Overview of main service offerings
5. **Demand Generation**: Detailed explanation of their approach
6. **Testimonials**: Client feedback and success stories
7. **Footer**: Contact information and additional links

## Technologies Used

- **HTML5**: Semantic markup
- **CSS3**: Modern styling with Flexbox and Grid
- **Vanilla JavaScript**: Smooth scrolling and interactive effects
- **Responsive Design**: Mobile-first approach

## Customization

The website is built with a single HTML file containing embedded CSS and JavaScript for easy customization:

- **Colors**: Modify the CSS custom properties at the top of the `<style>` section
- **Content**: Update text directly in the HTML
- **Styling**: Adjust CSS rules in the `<style>` section
- **Functionality**: Modify JavaScript at the bottom of the file

## Browser Compatibility

This website works in all modern browsers including:
- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## Notes

- This is a simplified recreation based on the original Dapper Agency website
- Some interactive elements and animations from the original may be simplified
- Images are replaced with placeholder styling
- Forms are non-functional (for display purposes only)

## Original Website

Visit the original Dapper Agency website at: https://www.dapper.agency/
